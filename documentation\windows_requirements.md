# Windows环境依赖清单
## 8类蜂巢语义分割训练项目

---

## 💻 **系统要求**

### **硬件要求**
- **GPU**: NVIDIA RTX 5070 12GB (推荐) 或其他支持CUDA的GPU
- **内存**: 16GB+ 系统内存
- **存储**: 50GB+ 可用磁盘空间
- **CPU**: 多核处理器 (推荐8核+)

### **操作系统**
- Windows 10/11 (64位)
- Windows Server 2019/2022 (推荐)

---

## 🔧 **必需软件安装**

### **1. Python环境**
```powershell
# 推荐使用Python 3.9
# 下载地址: https://www.python.org/downloads/windows/
# 或使用Chocolatey安装
choco install python39 -y
```

### **2. CUDA Toolkit**
```powershell
# CUDA 11.8 (与PyTorch兼容)
# 下载地址: https://developer.nvidia.com/cuda-11-8-0-download-archive
# 选择Windows x86_64版本
```

### **3. cuDNN**
```powershell
# cuDNN 8.7.0 for CUDA 11.8
# 下载地址: https://developer.nvidia.com/cudnn
# 需要NVIDIA开发者账号
```

### **4. Git (可选)**
```powershell
# 用于版本控制
choco install git -y
```

---

## 📦 **Python依赖包**

### **核心深度学习包**
```bash
# PyTorch生态系统
torch==2.0.1+cu118
torchvision==0.15.2+cu118
torchaudio==2.0.2+cu118

# 安装命令
pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu118
```

### **模型和变换器**
```bash
# Transformer模型库
timm==0.9.2                    # 预训练模型
transformers==4.30.2           # Hugging Face transformers
einops==0.6.1                  # 张量操作

pip install timm==0.9.2 transformers==4.30.2 einops==0.6.1
```

### **计算机视觉**
```bash
# 图像处理
opencv-python==********        # OpenCV
Pillow==10.0.0                  # PIL图像库
albumentations==1.3.1           # 数据增强
scikit-image==0.21.0            # 科学图像处理

pip install opencv-python==******** Pillow==10.0.0 albumentations==1.3.1 scikit-image==0.21.0
```

### **数据科学**
```bash
# 数据处理和分析
numpy==1.24.3                  # 数值计算
pandas==2.0.3                  # 数据分析
scikit-learn==1.3.0            # 机器学习
matplotlib==3.7.2              # 绘图
seaborn==0.12.2                # 统计绘图

pip install numpy==1.24.3 pandas==2.0.3 scikit-learn==1.3.0 matplotlib==3.7.2 seaborn==0.12.2
```

### **训练工具**
```bash
# 训练辅助工具
tqdm==4.65.0                   # 进度条
tensorboard==2.13.0            # 可视化
wandb==0.15.8                  # 实验跟踪
pyyaml==6.0.1                  # YAML配置

pip install tqdm==4.65.0 tensorboard==2.13.0 wandb==0.15.8 pyyaml==6.0.1
```

### **评估指标**
```bash
# 评估和指标
torchmetrics==1.0.3            # PyTorch指标库

pip install torchmetrics==1.0.3
```

### **系统工具**
```bash
# 系统监控和工具
psutil==5.9.5                  # 系统信息
colorama==0.4.6                # 彩色输出
rich==13.5.2                   # 富文本显示

pip install psutil==5.9.5 colorama==0.4.6 rich==13.5.2
```

---

## 🔧 **环境配置**

### **环境变量设置**
```powershell
# CUDA路径
$env:CUDA_PATH = "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8"
$env:CUDA_PATH_V11_8 = "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8"

# 添加到系统PATH
$env:PATH += ";C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin"
$env:PATH += ";C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\libnvvp"

# PyTorch优化
$env:PYTORCH_CUDA_ALLOC_CONF = "max_split_size_mb:512"
$env:OMP_NUM_THREADS = "4"
```

### **Python虚拟环境**
```powershell
# 创建虚拟环境
python -m venv bee_experiment

# 激活虚拟环境
.\bee_experiment\Scripts\Activate.ps1

# 升级pip
python -m pip install --upgrade pip
```

---

## ✅ **环境验证**

### **CUDA验证**
```powershell
# 检查CUDA安装
nvidia-smi
nvcc --version
```

### **Python环境验证**
```python
# 验证PyTorch CUDA支持
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"CUDA版本: {torch.version.cuda}")
print(f"GPU数量: {torch.cuda.device_count()}")
if torch.cuda.is_available():
    print(f"GPU名称: {torch.cuda.get_device_name(0)}")
    print(f"显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
```

### **关键包验证**
```python
# 验证关键包导入
packages = [
    'torch', 'torchvision', 'timm', 'transformers',
    'cv2', 'PIL', 'albumentations', 'numpy',
    'pandas', 'sklearn', 'matplotlib', 'tqdm',
    'tensorboard', 'yaml', 'torchmetrics'
]

for pkg in packages:
    try:
        __import__(pkg)
        print(f"✅ {pkg}")
    except ImportError as e:
        print(f"❌ {pkg}: {e}")
```

---

## 🚀 **一键安装脚本**

### **完整环境安装**
```powershell
# install_environment.ps1
Write-Host "🚀 开始安装Windows训练环境..." -ForegroundColor Green

# 1. 创建虚拟环境
python -m venv bee_experiment
& ".\bee_experiment\Scripts\Activate.ps1"

# 2. 升级pip
python -m pip install --upgrade pip

# 3. 安装PyTorch
pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu118

# 4. 安装其他依赖
pip install timm==0.9.2 transformers==4.30.2 einops==0.6.1
pip install opencv-python==******** Pillow==10.0.0 albumentations==1.3.1 scikit-image==0.21.0
pip install numpy==1.24.3 pandas==2.0.3 scikit-learn==1.3.0 matplotlib==3.7.2 seaborn==0.12.2
pip install tqdm==4.65.0 tensorboard==2.13.0 wandb==0.15.8 pyyaml==6.0.1
pip install torchmetrics==1.0.3 psutil==5.9.5 colorama==0.4.6 rich==13.5.2

# 5. 验证安装
python -c "
import torch
print(f'✅ PyTorch: {torch.__version__}')
print(f'✅ CUDA: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'✅ GPU: {torch.cuda.get_device_name(0)}')
"

Write-Host "✅ 环境安装完成!" -ForegroundColor Green
```

---

## ⚠️ **常见问题**

### **CUDA问题**
```
问题：torch.cuda.is_available() 返回 False
解决：
1. 检查GPU驱动版本
2. 重新安装CUDA Toolkit
3. 确认PyTorch CUDA版本匹配
```

### **内存问题**
```
问题：训练时显存不足
解决：
1. 降低batch_size
2. 启用混合精度训练
3. 使用梯度检查点
```

### **路径问题**
```
问题：找不到文件或目录
解决：
1. 检查路径分隔符（使用\而不是/）
2. 确认文件权限
3. 使用绝对路径
```

---

## 📊 **性能优化建议**

### **RTX 5070优化**
```yaml
# 推荐配置
batch_size: 6              # 训练批次
val_batch_size: 3          # 验证批次
num_workers: 4             # 数据加载进程
mixed_precision: true      # 混合精度
pin_memory: true           # 内存固定
```

### **系统优化**
```powershell
# Windows性能优化
# 1. 关闭Windows Defender实时保护（训练期间）
# 2. 设置高性能电源计划
# 3. 关闭不必要的后台程序
# 4. 确保足够的虚拟内存
```

这样就完成了Windows环境的完整配置！
