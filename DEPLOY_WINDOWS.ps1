# Windows一键部署脚本
# 8类蜂巢语义分割训练项目

param(
    [string]$InstallPath = "C:\Users\<USER>\Desktop\BeeSegmentation"
)

Write-Host "🚀 开始Windows桌面一键部署..." -ForegroundColor Green
Write-Host "目标路径: $InstallPath" -ForegroundColor Cyan
Write-Host "桌面部署: 专为桌面环境优化" -ForegroundColor Yellow
Write-Host "=========================================="

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请以管理员身份运行PowerShell" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}

try {
    # 创建目标目录（桌面环境）
    Write-Host "📁 创建桌面项目目录..." -ForegroundColor Yellow

    # 确保桌面目录存在
    $desktopPath = Split-Path $InstallPath -Parent
    if (!(Test-Path $desktopPath)) {
        Write-Host "⚠️ 桌面路径不存在，创建用户目录..." -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $desktopPath -Force | Out-Null
    }

    if (!(Test-Path $InstallPath)) {
        New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
        Write-Host "✅ 桌面项目目录创建成功" -ForegroundColor Green
    }

    # 设置桌面目录权限（确保当前用户有完全控制权限）
    $acl = Get-Acl $InstallPath
    $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule($currentUser, "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    Set-Acl -Path $InstallPath -AclObject $acl
    Write-Host "✅ 桌面目录权限设置完成" -ForegroundColor Green
    
    # 复制项目文件
    Write-Host "📋 复制项目文件..." -ForegroundColor Yellow
    $currentDir = Get-Location
    
    Copy-Item -Path "$currentDir\training_refactored" -Destination "$InstallPath\" -Recurse -Force
    Copy-Item -Path "$currentDir\experiments" -Destination "$InstallPath\" -Recurse -Force
    Copy-Item -Path "$currentDir\reprocessed_data" -Destination "$InstallPath\" -Recurse -Force
    Copy-Item -Path "$currentDir\windows_setup\*" -Destination "$InstallPath\" -Force -ErrorAction SilentlyContinue
    
    Write-Host "✅ 文件复制完成" -ForegroundColor Green
    
    # 环境配置
    Write-Host "🔧 配置Python环境..." -ForegroundColor Yellow
    Set-Location $InstallPath
    
    if (Test-Path "setup_windows_environment.ps1") {
        & ".\setup_windows_environment.ps1"
    }
    
    if (Test-Path "install_python_packages.ps1") {
        & ".\install_python_packages.ps1"
    }
    
    # GPU优化
    Write-Host "🎮 RTX 5070优化..." -ForegroundColor Yellow
    if (Test-Path "optimize_rtx5070.ps1") {
        & ".\optimize_rtx5070.ps1"
    }
    
    # 验证部署
    Write-Host "✅ 验证部署..." -ForegroundColor Yellow
    Set-Location "$InstallPath\training_refactored"
    
    # 激活虚拟环境并测试
    & "$InstallPath\bee_experiment\Scripts\Activate.ps1"
    python -c "
import torch
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU: {torch.cuda.get_device_name(0)}')
    print(f'显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB')
print('✅ 环境验证成功')
"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "=========================================="
        Write-Host "🎉 Windows桌面部署成功完成!" -ForegroundColor Green
        Write-Host "项目路径: $InstallPath" -ForegroundColor Cyan
        Write-Host "桌面位置: C:\Users\<USER>\Desktop\BeeSegmentation" -ForegroundColor Cyan
        Write-Host "启动训练: cd `"$InstallPath\training_refactored`" && .\start_windows_training.ps1" -ForegroundColor Yellow
        Write-Host "快捷方式: 双击桌面上的BeeSegmentation文件夹" -ForegroundColor Yellow
        Write-Host "=========================================="
    } else {
        throw "环境验证失败"
    }
    
} catch {
    Write-Host "❌ 部署失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查错误信息并重试" -ForegroundColor Yellow
} finally {
    Set-Location $currentDir
}

Read-Host "按Enter键退出"
