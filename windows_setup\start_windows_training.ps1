# Windows版本 - 8类蜂巢语义分割训练启动脚本
# RTX 5070 12GB优化版本

param(
    [string]$ConfigFile = "configs\windows_segmentation_config.yaml",
    [string]$ProjectRoot = "C:\Users\<USER>\Desktop\BeeSegmentation",
    [switch]$Debug,
    [switch]$Resume
)

Write-Host "🚀 启动Windows版本蜂巢语义分割训练" -ForegroundColor Green
Write-Host "GPU: NVIDIA RTX 5070 12GB" -ForegroundColor Yellow
Write-Host "项目: UperNet + MAE 8类语义分割" -ForegroundColor Cyan
Write-Host "=========================================="

# 设置工作目录
Set-Location $ProjectRoot\training_refactored

# 激活虚拟环境
Write-Host "🐍 激活Python虚拟环境..." -ForegroundColor Cyan
& "$ProjectRoot\bee_experiment\Scripts\Activate.ps1"

# 验证环境
Write-Host "✅ 验证训练环境..." -ForegroundColor Cyan
python -c "
import torch
import sys
print(f'Python版本: {sys.version}')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA版本: {torch.version.cuda}')
    print(f'GPU: {torch.cuda.get_device_name(0)}')
    total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    print(f'显存: {total_memory:.1f} GB')
else:
    print('❌ CUDA不可用!')
    exit(1)
"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 环境验证失败" -ForegroundColor Red
    exit 1
}

# 检查配置文件
if (!(Test-Path $ConfigFile)) {
    Write-Host "❌ 配置文件不存在: $ConfigFile" -ForegroundColor Red
    exit 1
}
Write-Host "✅ 配置文件: $ConfigFile" -ForegroundColor Green

# 检查关键文件
Write-Host "📋 检查关键文件..." -ForegroundColor Cyan
$criticalPaths = @{
    "训练脚本" = "scripts\train_semantic_segmentation.py"
    "MAE权重" = "..\experiments\phase6_mae_vit_base\outputs"
    "训练数据" = "..\reprocessed_data\patches\train"
    "验证数据" = "..\reprocessed_data\patches\val"
    "模型文件" = "models"
    "工具文件" = "utils"
}

$missingPaths = @()
foreach ($item in $criticalPaths.GetEnumerator()) {
    if (Test-Path $item.Value) {
        Write-Host "  ✅ $($item.Key): $($item.Value)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $($item.Key): $($item.Value)" -ForegroundColor Red
        $missingPaths += $item.Value
    }
}

if ($missingPaths.Count -gt 0) {
    Write-Host "❌ 缺失关键文件，无法启动训练" -ForegroundColor Red
    exit 1
}

# 创建输出目录
$outputDir = "outputs\windows_upernet_mae_segmentation"
if (!(Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force
    Write-Host "📁 创建输出目录: $outputDir" -ForegroundColor Cyan
}

# 创建日志目录
$logDir = "logs"
if (!(Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force
}

# 设置环境变量
$env:CUDA_VISIBLE_DEVICES = "0"  # RTX 5070
$env:PYTHONPATH = "$ProjectRoot\training_refactored"

# Windows特定优化
$env:PYTORCH_CUDA_ALLOC_CONF = "max_split_size_mb:512"
$env:OMP_NUM_THREADS = "4"

# 显示训练配置
Write-Host "🔧 训练配置:" -ForegroundColor Yellow
Write-Host "  配置文件: $ConfigFile"
Write-Host "  输出目录: $outputDir"
Write-Host "  调试模式: $Debug"
Write-Host "  恢复训练: $Resume"
Write-Host "  GPU设备: CUDA:0 (RTX 5070)"

# 构建训练命令
$trainArgs = @(
    "scripts\train_semantic_segmentation.py",
    "--config", $ConfigFile
)

if ($Debug) {
    $trainArgs += "--debug"
}

if ($Resume) {
    $trainArgs += "--resume"
}

# GPU内存预检查
Write-Host "🎮 GPU内存预检查..." -ForegroundColor Cyan
python -c "
import torch
if torch.cuda.is_available():
    device = torch.device('cuda:0')
    # 清理缓存
    torch.cuda.empty_cache()
    
    # 检查可用内存
    total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    allocated = torch.cuda.memory_allocated(0) / 1024**3
    available = total_memory - allocated
    
    print(f'总显存: {total_memory:.2f} GB')
    print(f'已使用: {allocated:.2f} GB')
    print(f'可用: {available:.2f} GB')
    
    if available < 8.0:
        print('⚠️ 可用显存不足8GB，可能影响训练')
    else:
        print('✅ 显存充足，可以开始训练')
"

# 启动训练
Write-Host "=========================================="
Write-Host "🚀 启动训练..." -ForegroundColor Green
Write-Host "命令: python $($trainArgs -join ' ')" -ForegroundColor Cyan
Write-Host "=========================================="

# 创建日志文件名
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logFile = "logs\windows_training_$timestamp.log"

# 启动训练进程
try {
    # 使用Start-Process启动训练，重定向输出到日志文件
    $processArgs = @{
        FilePath = "python"
        ArgumentList = $trainArgs
        RedirectStandardOutput = $logFile
        RedirectStandardError = "$logFile.error"
        NoNewWindow = $true
        PassThru = $true
    }
    
    $trainProcess = Start-Process @processArgs
    
    Write-Host "✅ 训练进程已启动" -ForegroundColor Green
    Write-Host "进程ID: $($trainProcess.Id)" -ForegroundColor Cyan
    Write-Host "日志文件: $logFile" -ForegroundColor Cyan
    Write-Host "错误日志: $logFile.error" -ForegroundColor Cyan
    
    # 监控训练进程
    Write-Host "🔍 监控训练进程..." -ForegroundColor Yellow
    Write-Host "按 Ctrl+C 停止监控（训练将继续在后台运行）"
    
    # 实时显示日志
    $logMonitor = {
        param($LogPath)
        if (Test-Path $LogPath) {
            Get-Content $LogPath -Wait -Tail 10
        }
    }
    
    # 启动日志监控
    Start-Job -ScriptBlock $logMonitor -ArgumentList $logFile | Out-Null
    
    # 等待用户中断或进程结束
    while (!$trainProcess.HasExited) {
        Start-Sleep -Seconds 5
        
        # 显示GPU状态
        if ((Get-Random -Maximum 12) -eq 0) {  # 约每分钟显示一次
            Write-Host "$(Get-Date -Format 'HH:mm:ss') - GPU状态检查..." -ForegroundColor DarkGray
            nvidia-smi --query-gpu=memory.used,memory.total,utilization.gpu,temperature.gpu --format=csv,noheader,nounits | ForEach-Object {
                Write-Host "  GPU: $_ " -ForegroundColor DarkGray
            }
        }
    }
    
    # 训练完成
    $exitCode = $trainProcess.ExitCode
    if ($exitCode -eq 0) {
        Write-Host "✅ 训练成功完成!" -ForegroundColor Green
    } else {
        Write-Host "❌ 训练异常结束，退出码: $exitCode" -ForegroundColor Red
        Write-Host "请检查错误日志: $logFile.error" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ 启动训练失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "=========================================="
Write-Host "训练日志: $logFile" -ForegroundColor Cyan
Write-Host "输出目录: $outputDir" -ForegroundColor Cyan
Write-Host "=========================================="
