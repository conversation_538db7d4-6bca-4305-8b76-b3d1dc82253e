# Windows部署指南
## 8类蜂巢语义分割训练项目

### 🎯 项目概述
- **项目**: 8类蜂巢语义分割训练 (UperNet + MAE)
- **架构**: MAE预训练ViT + UperNet分割头
- **目标GPU**: NVIDIA RTX 5070 12GB
- **数据**: 2.4GB训练数据，8类语义标签

### 📦 包含内容
```
bee_segmentation_windows_download/
├── training_refactored/          # 训练代码（已清理Linux文件）
├── experiments/                  # MAE预训练权重 (~1.3GB)
├── reprocessed_data/            # 训练数据集 (~2.4GB)
├── windows_setup/               # Windows安装脚本
├── documentation/               # 详细文档
├── DEPLOY_WINDOWS.ps1           # 一键部署脚本
└── README_WINDOWS.md            # 本文件
```

### 🚀 快速部署

#### 1. 系统要求
- Windows 10/11 或 Windows Server
- NVIDIA RTX 5070 12GB (或其他CUDA GPU)
- 16GB+ 系统内存
- 50GB+ 可用磁盘空间

#### 2. 桌面一键部署
```powershell
# 1. 解压下载的目录到桌面：C:\Users\<USER>\Desktop\bee_segmentation_windows_download
# 2. 以管理员身份运行PowerShell
# 3. 进入解压目录
cd "C:\Users\<USER>\Desktop\bee_segmentation_windows_download"

# 4. 执行桌面一键部署
.\DEPLOY_WINDOWS.ps1

# 5. 等待自动完成（30-60分钟）
# 6. 项目将部署到：C:\Users\<USER>\Desktop\BeeSegmentation
```

#### 3. 启动训练
```powershell
# 部署完成后
cd "C:\Users\<USER>\Desktop\BeeSegmentation\training_refactored"
.\start_windows_training.ps1
```

### 📊 预期性能
- **训练速度**: ~1.5-2.0 it/s
- **批次大小**: 6 (训练) / 3 (验证)
- **显存使用**: ~8-10GB
- **训练时间**: 7-10天 (300 epochs)

### 🔧 手动部署（如果一键部署失败）

#### 1. 环境配置
```powershell
.\windows_setup\setup_windows_environment.ps1
.\windows_setup\install_python_packages.ps1
```

#### 2. 文件部署
```powershell
# 复制文件到 C:\Users\<USER>\Desktop\BeeSegmentation\
Copy-Item -Path "training_refactored" -Destination "C:\Users\<USER>\Desktop\BeeSegmentation\" -Recurse
Copy-Item -Path "experiments" -Destination "C:\Users\<USER>\Desktop\BeeSegmentation\" -Recurse
Copy-Item -Path "reprocessed_data" -Destination "C:\Users\<USER>\Desktop\BeeSegmentation\" -Recurse
```

#### 3. GPU优化
```powershell
.\windows_setup\optimize_rtx5070.ps1
```

### 🆘 故障排除

#### 常见问题
1. **权限不足**: 确保以管理员身份运行
2. **CUDA不可用**: 检查GPU驱动和CUDA安装
3. **Python环境**: 重新运行环境配置脚本
4. **路径错误**: 确保使用Windows路径格式

#### 获取帮助
- 查看 `documentation/` 目录中的详细文档
- 检查部署日志文件
- 验证系统要求

### ✅ 验证清单
部署完成后验证：
- [ ] Python虚拟环境激活成功
- [ ] CUDA和PyTorch正常工作
- [ ] GPU正确识别
- [ ] 训练脚本可执行
- [ ] MAE权重文件存在
- [ ] 训练数据完整

### 📞 技术支持
如遇问题，请检查：
- 系统要求是否满足
- 错误日志信息
- GPU驱动版本
- Python环境配置

---
**预计部署时间**: 30-60分钟
**预计训练时间**: 7-10天 (300 epochs)
