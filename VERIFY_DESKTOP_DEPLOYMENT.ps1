# 桌面部署验证脚本
# 验证8类蜂巢语义分割训练项目在桌面环境的部署状态

param(
    [string]$ProjectPath = "C:\Users\<USER>\Desktop\BeeSegmentation"
)

Write-Host "🔍 验证桌面部署状态..." -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Cyan
Write-Host "=========================================="

$allChecks = @()
$passedChecks = 0
$totalChecks = 0

# 检查函数
function Test-PathExists {
    param([string]$Path, [string]$Description)
    
    $global:totalChecks++
    if (Test-Path $Path) {
        Write-Host "✅ $Description" -ForegroundColor Green
        $global:passedChecks++
        $global:allChecks += @{Status="✅"; Description=$Description; Path=$Path}
        return $true
    } else {
        Write-Host "❌ $Description" -ForegroundColor Red
        $global:allChecks += @{Status="❌"; Description=$Description; Path=$Path}
        return $false
    }
}

# 1. 验证桌面环境
Write-Host "🖥️ 验证桌面环境..." -ForegroundColor Yellow

$desktopPath = [Environment]::GetFolderPath("Desktop")
Write-Host "系统桌面路径: $desktopPath" -ForegroundColor Cyan

$userDesktopPath = "C:\Users\<USER>\Desktop"
Test-PathExists $userDesktopPath "用户桌面目录存在"

# 2. 验证项目目录结构
Write-Host "📁 验证项目目录结构..." -ForegroundColor Yellow

$criticalPaths = @{
    "项目根目录" = $ProjectPath
    "训练代码目录" = "$ProjectPath\training_refactored"
    "脚本目录" = "$ProjectPath\training_refactored\scripts"
    "配置目录" = "$ProjectPath\training_refactored\configs"
    "核心模块目录" = "$ProjectPath\training_refactored\core"
    "工具目录" = "$ProjectPath\training_refactored\utils"
    "实验目录" = "$ProjectPath\experiments"
    "MAE权重目录" = "$ProjectPath\experiments\phase6_mae_vit_base\outputs\old_training_backup"
    "训练数据目录" = "$ProjectPath\reprocessed_data\patches"
    "训练数据子目录" = "$ProjectPath\reprocessed_data\patches\train"
    "验证数据子目录" = "$ProjectPath\reprocessed_data\patches\val"
}

foreach ($item in $criticalPaths.GetEnumerator()) {
    Test-PathExists $item.Value $item.Key
}

# 3. 验证关键文件
Write-Host "📄 验证关键文件..." -ForegroundColor Yellow

$criticalFiles = @{
    "MAE权重文件" = "$ProjectPath\experiments\phase6_mae_vit_base\outputs\old_training_backup\best_checkpoint_epoch_0572.pth"
    "主配置文件" = "$ProjectPath\training_refactored\configs\semantic_segmentation_config_correct.yaml"
    "训练脚本" = "$ProjectPath\training_refactored\scripts\train_semantic_segmentation.py"
    "数据集信息" = "$ProjectPath\reprocessed_data\patches\dataset_info.json"
    "Python虚拟环境" = "$ProjectPath\bee_experiment\Scripts\activate.bat"
}

foreach ($item in $criticalFiles.GetEnumerator()) {
    Test-PathExists $item.Value $item.Key
}

# 4. 验证Python环境
Write-Host "🐍 验证Python环境..." -ForegroundColor Yellow

if (Test-Path "$ProjectPath\bee_experiment\Scripts\python.exe") {
    Write-Host "✅ Python虚拟环境已安装" -ForegroundColor Green
    $passedChecks++
    
    # 激活虚拟环境并测试
    try {
        & "$ProjectPath\bee_experiment\Scripts\Activate.ps1"
        $pythonVersion = & "$ProjectPath\bee_experiment\Scripts\python.exe" --version 2>&1
        Write-Host "  Python版本: $pythonVersion" -ForegroundColor Cyan
        
        # 测试关键包
        $packages = @("torch", "torchvision", "timm", "transformers", "cv2", "yaml")
        foreach ($pkg in $packages) {
            try {
                & "$ProjectPath\bee_experiment\Scripts\python.exe" -c "import $pkg; print(f'✅ $pkg')" 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "  ✅ $pkg 已安装" -ForegroundColor Green
                } else {
                    Write-Host "  ❌ $pkg 未安装" -ForegroundColor Red
                }
            } catch {
                Write-Host "  ❌ $pkg 测试失败" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "❌ Python环境测试失败" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Python虚拟环境未安装" -ForegroundColor Red
}
$totalChecks++

# 5. 验证GPU环境
Write-Host "🎮 验证GPU环境..." -ForegroundColor Yellow

try {
    & "$ProjectPath\bee_experiment\Scripts\python.exe" -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU名称: {torch.cuda.get_device_name(0)}')
    print(f'显存大小: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB')
    print('✅ GPU环境正常')
else:
    print('❌ CUDA不可用')
" 2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ GPU环境验证通过" -ForegroundColor Green
        $passedChecks++
    } else {
        Write-Host "❌ GPU环境验证失败" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ GPU环境测试异常" -ForegroundColor Red
}
$totalChecks++

# 6. 验证配置文件路径
Write-Host "🔧 验证配置文件路径..." -ForegroundColor Yellow

$configFile = "$ProjectPath\training_refactored\configs\semantic_segmentation_config_correct.yaml"
if (Test-Path $configFile) {
    $configContent = Get-Content $configFile -Raw
    
    # 检查路径格式
    if ($configContent -match "C:\\\\Users\\\\<USER>\\\\Desktop\\\\BeeSegmentation") {
        Write-Host "✅ 配置文件路径格式正确" -ForegroundColor Green
        $passedChecks++
    } else {
        Write-Host "❌ 配置文件路径格式错误" -ForegroundColor Red
    }
} else {
    Write-Host "❌ 配置文件不存在" -ForegroundColor Red
}
$totalChecks++

# 7. 验证权限
Write-Host "🔐 验证目录权限..." -ForegroundColor Yellow

try {
    $testFile = "$ProjectPath\permission_test.tmp"
    "test" | Out-File -FilePath $testFile -Force
    Remove-Item $testFile -Force
    Write-Host "✅ 目录权限正常" -ForegroundColor Green
    $passedChecks++
} catch {
    Write-Host "❌ 目录权限不足" -ForegroundColor Red
}
$totalChecks++

# 生成验证报告
Write-Host "=========================================="
Write-Host "📊 验证报告" -ForegroundColor Yellow
Write-Host "通过检查: $passedChecks / $totalChecks" -ForegroundColor Cyan

$successRate = [math]::Round(($passedChecks / $totalChecks) * 100, 1)
if ($successRate -ge 90) {
    Write-Host "🎉 部署状态: 优秀 ($successRate%)" -ForegroundColor Green
    Write-Host "✅ 可以开始训练!" -ForegroundColor Green
} elseif ($successRate -ge 70) {
    Write-Host "⚠️ 部署状态: 良好 ($successRate%)" -ForegroundColor Yellow
    Write-Host "建议检查失败项目后再开始训练" -ForegroundColor Yellow
} else {
    Write-Host "❌ 部署状态: 需要修复 ($successRate%)" -ForegroundColor Red
    Write-Host "请修复失败项目后再开始训练" -ForegroundColor Red
}

# 详细检查结果
Write-Host "`n📋 详细检查结果:" -ForegroundColor Yellow
foreach ($check in $allChecks) {
    Write-Host "$($check.Status) $($check.Description)" -ForegroundColor $(if ($check.Status -eq "✅") { "Green" } else { "Red" })
}

# 下一步建议
Write-Host "`n🚀 下一步操作:" -ForegroundColor Yellow
if ($successRate -ge 90) {
    Write-Host "1. 启动训练:" -ForegroundColor Green
    Write-Host "   cd `"$ProjectPath\training_refactored`"" -ForegroundColor Cyan
    Write-Host "   .\start_windows_training.ps1" -ForegroundColor Cyan
} else {
    Write-Host "1. 修复失败的检查项目" -ForegroundColor Red
    Write-Host "2. 重新运行验证脚本" -ForegroundColor Red
    Write-Host "3. 确认所有检查通过后再启动训练" -ForegroundColor Red
}

Write-Host "=========================================="
Read-Host "按Enter键退出"
