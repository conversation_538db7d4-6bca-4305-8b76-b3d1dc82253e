# Windows版本 - 8类蜂巢语义分割训练配置
# 针对RTX 5070 12GB显存优化
# UperNet + MAE架构

model:
  feature_extractor:
    type: "MultiLayerViTFeatureExtractor"
    # Windows路径格式
    mae_checkpoint: "C:\\BeeSegmentation\\experiments\\phase6_mae_vit_base\\outputs\\best_checkpoint_epoch_0572.pth"
    layers: [3, 6, 9, 12]
    img_size: 512
    patch_size: 16
    embed_dim: 768
    depth: 12
    num_heads: 12

  segmentation_head:
    type: "UPerNetHead"
    in_channels: [768, 768, 768, 768]
    in_index: [0, 1, 2, 3]
    pool_scales: [1, 2, 3, 6]
    channels: 384  # RTX 5070 12GB优化：从512降到384
    dropout_ratio: 0.1
    num_classes: 8
    fpn_dim: 192  # 从256降到192
    norm_cfg:
      type: "GroupNorm"
      num_groups: 24  # 从32降到24
    align_corners: false

# 数据配置 - Windows路径 + RTX 5070优化
data:
  # Windows路径格式
  train_dir: "C:\\BeeSegmentation\\reprocessed_data\\patches\\train"
  val_dir: "C:\\BeeSegmentation\\reprocessed_data\\patches\\val"

  # RTX 5070 12GB显存优化
  image_size: [512, 512]
  num_classes: 8
  batch_size: 6  # 针对12GB显存优化
  val_batch_size: 3  # 验证批次大小
  num_workers: 4  # Windows优化的进程数
  pin_memory: true  # Windows下可以启用
  prefetch_factor: 2

  # 数据增强
  augmentation:
    horizontal_flip: 0.5
    vertical_flip: 0.5
    rotation: 15
    color_jitter:
      brightness: 0.2
      contrast: 0.2
      saturation: 0.2
      hue: 0.1

  # 归一化
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]

# 训练配置 - RTX 5070优化
training:
  epochs: 300
  learning_rate: 0.0003
  weight_decay: 0.0001
  optimizer: "adamw"
  betas: [0.9, 0.999]
  
  # 学习率调度
  lr_scheduler: "cosine"
  warmup_epochs: 30
  
  # 损失函数
  loss:
    type: "combined"
    dice_weight: 0.3
    ce_weight: 0.3
    focal_weight: 0.4
    focal_alpha: 0.25
    focal_gamma: 2.0
  
  # 训练策略
  gradient_clip_norm: 1.0
  mixed_precision: true  # RTX 5070支持混合精度
  early_stopping_patience: 20
  
  # 验证和保存
  val_interval: 1
  save_interval: 5
  
  # 内存管理 - RTX 5070优化
  empty_cache_every_n_steps: 100  # 每100步清理缓存
  max_grad_norm: 1.0

# 验证配置
validation:
  batch_size: 3  # RTX 5070优化
  num_workers: 2
  pin_memory: true
  drop_last: false
  
  # 验证策略
  compute_detailed_metrics: true
  save_predictions: false  # 节省存储空间
  clear_cache_after_val: true

# 输出配置 - Windows路径
output:
  # Windows路径格式
  output_dir: "C:\\BeeSegmentation\\experiments"
  experiment_name: "windows_upernet_mae_segmentation"
  log_file: "windows_training.log"
  log_level: "INFO"
  save_top_k: 3
  save_every_n_epochs: 10
  log_every_n_steps: 50

# 系统配置 - Windows + RTX 5070
system:
  device: "cuda:0"  # RTX 5070
  num_gpus: 1
  debug_mode: false
  
  # RTX 5070内存管理
  max_memory_usage: 0.85  # 12GB显存的85%
  memory_cleanup_interval: 100

# 调试配置
debug:
  enable: true
  log_memory_usage: true
  log_gpu_stats: true
  save_memory_snapshots: false

# Windows特定配置
windows_specific:
  # 路径分隔符
  path_separator: "\\"
  
  # 进程配置
  multiprocessing_start_method: "spawn"  # Windows推荐
  
  # 文件系统
  use_long_paths: true  # 支持长路径
  
  # 性能优化
  disable_windows_defender_realtime: false  # 建议手动关闭实时保护

# RTX 5070特定优化
rtx_5070_optimizations:
  # 显存优化
  memory_efficient_attention: true
  gradient_checkpointing: false  # 12GB显存足够，不需要
  
  # 计算优化
  use_amp: true  # 自动混合精度
  cudnn_benchmark: true  # RTX 5070优化
  
  # 批次优化
  optimal_batch_size: 6  # 经验值
  max_batch_size: 8  # 最大安全批次

# 8类蜂巢语义分割类别
class_names:
  - "background"      # 0: 背景
  - "capped_brood"    # 1: 封盖子
  - "eggs"            # 2: 卵
  - "honey"           # 3: 蜂蜜
  - "honeycomb"       # 4: 蜂巢结构
  - "larvae"          # 5: 幼虫
  - "nectar"          # 6: 花蜜
  - "pollen"          # 7: 花粉

# 评估指标配置
metrics:
  compute_detailed_metrics: true
  save_confusion_matrix: true
  save_class_metrics: true
  
  # 计算的指标
  metrics_list:
    - "iou"           # IoU (Intersection over Union)
    - "miou"          # 平均IoU
    - "accuracy"      # 像素准确率
    - "dice"          # Dice系数
    - "precision"     # 精确率
    - "recall"        # 召回率
    - "f1_score"      # F1分数
