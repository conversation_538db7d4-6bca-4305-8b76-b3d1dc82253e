# Python依赖包安装脚本 - Windows版本
# 8类蜂巢语义分割训练项目

Write-Host "📦 开始安装Python依赖包..." -ForegroundColor Green
Write-Host "目标环境: Windows Server + RTX 5070 12GB" -ForegroundColor Yellow
Write-Host "=========================================="

# 激活虚拟环境
$projectRoot = "C:\Users\<USER>\Desktop\BeeSegmentation"
& "$projectRoot\bee_experiment\Scripts\Activate.ps1"

# 验证虚拟环境
Write-Host "🐍 当前Python环境:"
python --version
Write-Host "虚拟环境路径: $env:VIRTUAL_ENV"

# 1. 安装PyTorch (CUDA 11.8版本)
Write-Host "🔥 安装PyTorch with CUDA 11.8..."
pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu118

# 2. 验证PyTorch CUDA支持
Write-Host "🎮 验证PyTorch CUDA支持..."
python -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA版本: {torch.version.cuda}')
    print(f'GPU数量: {torch.cuda.device_count()}')
    for i in range(torch.cuda.device_count()):
        print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
else:
    print('❌ CUDA不可用，请检查CUDA安装')
"

# 3. 安装深度学习相关包
Write-Host "🧠 安装深度学习相关包..."
pip install timm==0.9.2
pip install transformers==4.30.2
pip install einops==0.6.1

# 4. 安装计算机视觉包
Write-Host "👁️ 安装计算机视觉包..."
pip install opencv-python==********
pip install Pillow==10.0.0
pip install albumentations==1.3.1
pip install scikit-image==0.21.0

# 5. 安装数据处理包
Write-Host "📊 安装数据处理包..."
pip install numpy==1.24.3
pip install pandas==2.0.3
pip install scikit-learn==1.3.0
pip install matplotlib==3.7.2
pip install seaborn==0.12.2

# 6. 安装训练工具包
Write-Host "🛠️ 安装训练工具包..."
pip install tqdm==4.65.0
pip install tensorboard==2.13.0
pip install wandb==0.15.8
pip install pyyaml==6.0.1

# 7. 安装评估指标包
Write-Host "📈 安装评估指标包..."
pip install torchmetrics==1.0.3

# 8. 安装其他工具包
Write-Host "🔧 安装其他工具包..."
pip install psutil==5.9.5
pip install colorama==0.4.6
pip install rich==13.5.2

# 9. 创建requirements.txt
Write-Host "📝 生成requirements.txt..."
pip freeze > "$projectRoot\requirements.txt"

# 10. 验证关键包安装
Write-Host "✅ 验证关键包安装..."
python -c "
import sys
packages = [
    'torch', 'torchvision', 'timm', 'transformers', 
    'cv2', 'PIL', 'albumentations', 'numpy', 
    'pandas', 'sklearn', 'matplotlib', 'tqdm',
    'tensorboard', 'yaml', 'torchmetrics'
]

print('📦 已安装的关键包:')
for pkg in packages:
    try:
        __import__(pkg)
        print(f'  ✅ {pkg}')
    except ImportError as e:
        print(f'  ❌ {pkg}: {e}')
"

# 11. GPU内存测试
Write-Host "🎮 GPU内存测试..."
python -c "
import torch
if torch.cuda.is_available():
    device = torch.device('cuda:0')
    print(f'GPU设备: {torch.cuda.get_device_name(0)}')
    
    # 获取GPU内存信息
    total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    print(f'总显存: {total_memory:.2f} GB')
    
    # 测试内存分配
    try:
        # 分配1GB测试张量
        test_tensor = torch.randn(1024, 1024, 256, device=device)
        allocated = torch.cuda.memory_allocated(0) / 1024**3
        print(f'测试分配: {allocated:.2f} GB')
        
        # 清理
        del test_tensor
        torch.cuda.empty_cache()
        print('✅ GPU内存测试通过')
    except Exception as e:
        print(f'❌ GPU内存测试失败: {e}')
else:
    print('❌ CUDA不可用')
"

Write-Host "=========================================="
Write-Host "✅ Python依赖包安装完成!" -ForegroundColor Green
Write-Host "下一步: 移植项目文件到Windows环境" -ForegroundColor Yellow
Write-Host "=========================================="
