# 项目文件移植脚本 - Linux到Windows
# 8类蜂巢语义分割训练项目

param(
    [Parameter(Mandatory=$true)]
    [string]$SourcePath,  # Linux项目源路径（通过网络共享或USB）
    
    [string]$TargetPath = "C:\Users\<USER>\Desktop\BeeSegmentation"
)

Write-Host "📁 开始移植项目文件到Windows..." -ForegroundColor Green
Write-Host "源路径: $SourcePath" -ForegroundColor Cyan
Write-Host "目标路径: $TargetPath" -ForegroundColor Cyan
Write-Host "=========================================="

# 检查源路径
if (!(Test-Path $SourcePath)) {
    Write-Host "❌ 源路径不存在: $SourcePath" -ForegroundColor Red
    Write-Host "请确保Linux项目文件可访问（网络共享/USB等）" -ForegroundColor Yellow
    exit 1
}

# 创建目标目录
if (!(Test-Path $TargetPath)) {
    New-Item -ItemType Directory -Path $TargetPath -Force
}

# 定义文件移植映射
$fileMappings = @{
    # 训练脚本
    "$SourcePath/training_refactored/scripts" = "$TargetPath/training_refactored/scripts"
    
    # 模型文件
    "$SourcePath/training_refactored/models" = "$TargetPath/training_refactored/models"
    
    # 工具文件
    "$SourcePath/training_refactored/utils" = "$TargetPath/training_refactored/utils"
    
    # 配置文件（需要路径转换）
    "$SourcePath/training_refactored/configs" = "$TargetPath/training_refactored/configs"
    
    # MAE预训练权重
    "$SourcePath/experiments/phase6_mae_vit_base/outputs" = "$TargetPath/experiments/phase6_mae_vit_base/outputs"
    
    # 数据文件
    "$SourcePath/reprocessed_data" = "$TargetPath/reprocessed_data"
}

# 执行文件复制
foreach ($mapping in $fileMappings.GetEnumerator()) {
    $source = $mapping.Key
    $target = $mapping.Value
    
    Write-Host "📂 复制: $source -> $target" -ForegroundColor Cyan
    
    if (Test-Path $source) {
        # 创建目标目录
        $targetDir = Split-Path $target -Parent
        if (!(Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force
        }
        
        # 复制文件
        Copy-Item -Path $source -Destination $target -Recurse -Force
        Write-Host "  ✅ 复制完成" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️ 源文件不存在，跳过" -ForegroundColor Yellow
    }
}

# 路径转换函数
function Convert-LinuxPathsToWindows {
    param([string]$FilePath)
    
    if (!(Test-Path $FilePath)) {
        return
    }
    
    Write-Host "🔄 转换路径格式: $FilePath" -ForegroundColor Cyan
    
    $content = Get-Content $FilePath -Raw
    
    # Linux路径 -> Windows路径转换
    $pathMappings = @{
        '/home/<USER>/PycharmProjects/20250629' = 'C:\Users\<USER>\Desktop\BeeSegmentation'
        '/home/<USER>/PycharmProjects/20250629/training_refactored' = 'C:\Users\<USER>\Desktop\BeeSegmentation\training_refactored'
        '/home/<USER>/PycharmProjects/20250629/reprocessed_data' = 'C:\Users\<USER>\Desktop\BeeSegmentation\reprocessed_data'
        '/home/<USER>/PycharmProjects/20250629/experiments' = 'C:\Users\<USER>\Desktop\BeeSegmentation\experiments'
        '/' = '\'
    }
    
    foreach ($mapping in $pathMappings.GetEnumerator()) {
        $content = $content -replace [regex]::Escape($mapping.Key), $mapping.Value
    }
    
    # 保存转换后的文件
    Set-Content -Path $FilePath -Value $content -Encoding UTF8
    Write-Host "  ✅ 路径转换完成" -ForegroundColor Green
}

# 转换配置文件中的路径
Write-Host "🔄 转换配置文件路径..." -ForegroundColor Yellow
$configFiles = Get-ChildItem -Path "$TargetPath/training_refactored/configs" -Filter "*.yaml" -Recurse
foreach ($configFile in $configFiles) {
    Convert-LinuxPathsToWindows -FilePath $configFile.FullName
}

# 转换Python脚本中的路径
Write-Host "🔄 转换Python脚本路径..." -ForegroundColor Yellow
$pythonFiles = Get-ChildItem -Path "$TargetPath/training_refactored" -Filter "*.py" -Recurse
foreach ($pythonFile in $pythonFiles) {
    Convert-LinuxPathsToWindows -FilePath $pythonFile.FullName
}

# 创建Windows特定的配置文件
Write-Host "📝 创建Windows特定配置..." -ForegroundColor Yellow

# 复制并修改主配置文件
$windowsConfig = "$TargetPath/training_refactored/configs/windows_segmentation_config.yaml"
if (Test-Path "$TargetPath/training_refactored/configs/semantic_segmentation_config_correct.yaml") {
    Copy-Item -Path "$TargetPath/training_refactored/configs/semantic_segmentation_config_correct.yaml" -Destination $windowsConfig
    Convert-LinuxPathsToWindows -FilePath $windowsConfig
}

# 验证关键文件
Write-Host "✅ 验证关键文件..." -ForegroundColor Yellow
$criticalFiles = @(
    "$TargetPath/training_refactored/scripts/train_semantic_segmentation.py",
    "$TargetPath/training_refactored/models",
    "$TargetPath/training_refactored/utils",
    "$TargetPath/experiments/phase6_mae_vit_base/outputs",
    "$TargetPath/reprocessed_data/patches/train",
    "$TargetPath/reprocessed_data/patches/val"
)

$missingFiles = @()
foreach ($file in $criticalFiles) {
    if (!(Test-Path $file)) {
        $missingFiles += $file
        Write-Host "  ❌ 缺失: $file" -ForegroundColor Red
    } else {
        Write-Host "  ✅ 存在: $file" -ForegroundColor Green
    }
}

# 生成移植报告
$reportPath = "$TargetPath/migration_report.txt"
$report = @"
Windows移植报告
生成时间: $(Get-Date)
========================================

源路径: $SourcePath
目标路径: $TargetPath

已移植的文件:
$(($fileMappings.GetEnumerator() | ForEach-Object { "- $($_.Key) -> $($_.Value)" }) -join "`n")

缺失的关键文件:
$(if ($missingFiles.Count -eq 0) { "无" } else { ($missingFiles | ForEach-Object { "- $_" }) -join "`n" })

下一步操作:
1. 检查MAE预训练权重文件
2. 验证数据文件完整性
3. 运行Windows训练脚本
4. 配置GPU环境

注意事项:
- 所有路径已转换为Windows格式
- 配置文件已针对RTX 5070优化
- 建议运行测试脚本验证环境
"@

Set-Content -Path $reportPath -Value $report -Encoding UTF8

Write-Host "=========================================="
if ($missingFiles.Count -eq 0) {
    Write-Host "✅ 项目移植完成!" -ForegroundColor Green
} else {
    Write-Host "⚠️ 项目移植完成，但有 $($missingFiles.Count) 个文件缺失" -ForegroundColor Yellow
}
Write-Host "移植报告: $reportPath" -ForegroundColor Cyan
Write-Host "下一步: 运行Windows训练脚本" -ForegroundColor Yellow
Write-Host "=========================================="
