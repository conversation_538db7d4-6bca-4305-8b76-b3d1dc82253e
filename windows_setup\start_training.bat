@echo off
REM Windows批处理版本 - 8类蜂巢语义分割训练启动脚本
REM RTX 5070 12GB优化版本

echo ========================================
echo 🚀 Windows版本蜂巢语义分割训练
echo GPU: NVIDIA RTX 5070 12GB
echo 项目: UperNet + MAE 8类语义分割
echo ========================================

REM 设置项目根目录
set PROJECT_ROOT=C:\Users\<USER>\Desktop\BeeSegmentation
set TRAINING_DIR=%PROJECT_ROOT%\training_refactored

REM 切换到训练目录
cd /d %TRAINING_DIR%

REM 激活虚拟环境
echo 🐍 激活Python虚拟环境...
call %PROJECT_ROOT%\bee_experiment\Scripts\activate.bat

REM 验证环境
echo ✅ 验证训练环境...
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"None\"}')"

if %ERRORLEVEL% neq 0 (
    echo ❌ 环境验证失败
    pause
    exit /b 1
)

REM 检查配置文件
set CONFIG_FILE=configs\windows_segmentation_config.yaml
if not exist %CONFIG_FILE% (
    echo ❌ 配置文件不存在: %CONFIG_FILE%
    pause
    exit /b 1
)
echo ✅ 配置文件: %CONFIG_FILE%

REM 检查关键目录
echo 📋 检查关键文件...
if not exist scripts\train_semantic_segmentation.py (
    echo ❌ 训练脚本不存在
    pause
    exit /b 1
)
if not exist ..\experiments\phase6_mae_vit_base\outputs (
    echo ❌ MAE权重目录不存在
    pause
    exit /b 1
)
if not exist ..\reprocessed_data\patches\train (
    echo ❌ 训练数据目录不存在
    pause
    exit /b 1
)
if not exist ..\reprocessed_data\patches\val (
    echo ❌ 验证数据目录不存在
    pause
    exit /b 1
)
echo ✅ 关键文件检查通过

REM 创建输出和日志目录
if not exist outputs\windows_upernet_mae_segmentation mkdir outputs\windows_upernet_mae_segmentation
if not exist logs mkdir logs

REM 设置环境变量
set CUDA_VISIBLE_DEVICES=0
set PYTHONPATH=%TRAINING_DIR%
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
set OMP_NUM_THREADS=4

REM 显示配置信息
echo 🔧 训练配置:
echo   配置文件: %CONFIG_FILE%
echo   GPU设备: CUDA:0 (RTX 5070)
echo   项目目录: %TRAINING_DIR%

REM 生成日志文件名
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set DATE=%%c%%a%%b
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set TIME=%%a%%b
set TIMESTAMP=%DATE%_%TIME%
set LOG_FILE=logs\windows_training_%TIMESTAMP%.log

echo ========================================
echo 🚀 启动训练...
echo 日志文件: %LOG_FILE%
echo ========================================

REM 启动训练
python scripts\train_semantic_segmentation.py --config %CONFIG_FILE% 2>&1 | tee %LOG_FILE%

REM 检查训练结果
if %ERRORLEVEL% equ 0 (
    echo ✅ 训练成功完成!
) else (
    echo ❌ 训练异常结束，退出码: %ERRORLEVEL%
    echo 请检查日志文件: %LOG_FILE%
)

echo ========================================
echo 训练日志: %LOG_FILE%
echo 输出目录: outputs\windows_upernet_mae_segmentation
echo ========================================

pause
