# Windows Server 环境配置脚本
# 8类蜂巢语义分割训练项目 - Windows移植版本

Write-Host "🚀 开始配置Windows Server环境用于蜂巢语义分割训练" -ForegroundColor Green
Write-Host "目标GPU: NVIDIA RTX 5070 12GB" -ForegroundColor Yellow
Write-Host "=========================================="

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请以管理员身份运行PowerShell" -ForegroundColor Yellow
    exit 1
}

# 设置执行策略
Write-Host "📋 设置PowerShell执行策略..."
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine -Force

# 1. 安装Chocolatey包管理器
Write-Host "📦 安装Chocolatey包管理器..."
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    Write-Host "✅ Chocolatey安装完成" -ForegroundColor Green
} else {
    Write-Host "✅ Chocolatey已安装" -ForegroundColor Green
}

# 2. 安装Python 3.9
Write-Host "🐍 安装Python 3.9..."
choco install python39 -y
refreshenv

# 验证Python安装
$pythonVersion = python --version 2>&1
Write-Host "Python版本: $pythonVersion" -ForegroundColor Cyan

# 3. 安装Git
Write-Host "📂 安装Git..."
choco install git -y

# 4. 安装CUDA Toolkit 11.8 (兼容PyTorch)
Write-Host "🎮 安装CUDA Toolkit 11.8..."
$cudaUrl = "https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda_11.8.0_522.06_windows.exe"
$cudaInstaller = "$env:TEMP\cuda_installer.exe"

Write-Host "下载CUDA安装程序..."
Invoke-WebRequest -Uri $cudaUrl -OutFile $cudaInstaller
Write-Host "运行CUDA安装程序（请手动完成安装）..."
Start-Process -FilePath $cudaInstaller -Wait

# 5. 安装cuDNN
Write-Host "📚 cuDNN安装提示:" -ForegroundColor Yellow
Write-Host "请手动下载并安装cuDNN 8.7.0 for CUDA 11.8" -ForegroundColor Yellow
Write-Host "下载地址: https://developer.nvidia.com/cudnn" -ForegroundColor Yellow

# 6. 设置环境变量
Write-Host "🔧 设置环境变量..."
$cudaPath = "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8"
$env:CUDA_PATH = $cudaPath
$env:CUDA_PATH_V11_8 = $cudaPath
[Environment]::SetEnvironmentVariable("CUDA_PATH", $cudaPath, "Machine")
[Environment]::SetEnvironmentVariable("CUDA_PATH_V11_8", $cudaPath, "Machine")

# 添加CUDA到PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
if ($currentPath -notlike "*$cudaPath\bin*") {
    $newPath = "$currentPath;$cudaPath\bin;$cudaPath\libnvvp"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
}

# 7. 创建项目目录
Write-Host "📁 创建项目目录结构..."
$projectRoot = "C:\Users\<USER>\Desktop\BeeSegmentation"
$directories = @(
    "$projectRoot\training_refactored",
    "$projectRoot\training_refactored\scripts",
    "$projectRoot\training_refactored\configs",
    "$projectRoot\training_refactored\models",
    "$projectRoot\training_refactored\utils",
    "$projectRoot\training_refactored\outputs",
    "$projectRoot\training_refactored\logs",
    "$projectRoot\reprocessed_data\patches\train",
    "$projectRoot\reprocessed_data\patches\val",
    "$projectRoot\experiments\phase6_mae_vit_base\outputs"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "创建目录: $dir" -ForegroundColor Cyan
    }
}

# 8. 安装Python虚拟环境
Write-Host "🐍 创建Python虚拟环境..."
cd $projectRoot
python -m venv bee_experiment
& "$projectRoot\bee_experiment\Scripts\Activate.ps1"

# 9. 升级pip
Write-Host "📦 升级pip..."
python -m pip install --upgrade pip

Write-Host "=========================================="
Write-Host "✅ Windows环境基础配置完成!" -ForegroundColor Green
Write-Host "下一步: 运行 install_python_packages.ps1 安装Python依赖" -ForegroundColor Yellow
Write-Host "项目根目录: $projectRoot" -ForegroundColor Cyan
Write-Host "=========================================="
