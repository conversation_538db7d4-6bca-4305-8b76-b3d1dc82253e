# 桌面部署指南
## 8类蜂巢语义分割训练项目 - 桌面环境专用

---

## 🎯 **桌面部署概述**

本指南专门针对Windows桌面环境优化，将项目部署到用户桌面目录，便于管理和访问。

### **部署路径**
- **下载位置**: `C:\Users\<USER>\Desktop\bee_segmentation_windows_download`
- **部署目标**: `C:\Users\<USER>\Desktop\BeeSegmentation`
- **桌面访问**: 双击桌面上的BeeSegmentation文件夹

---

## 🚀 **快速部署步骤**

### **步骤1: 下载解压**
```
1. 下载整个 bee_segmentation_windows_download 目录
2. 解压到桌面：C:\Users\<USER>\Desktop\bee_segmentation_windows_download
3. 确认解压后包含以下文件：
   ✅ DEPLOY_WINDOWS.ps1
   ✅ training_refactored/
   ✅ experiments/
   ✅ reprocessed_data/
   ✅ windows_setup/
```

### **步骤2: 桌面一键部署**
```powershell
# 1. 以管理员身份运行PowerShell
# 2. 进入解压目录
cd "C:\Users\<USER>\Desktop\bee_segmentation_windows_download"

# 3. 执行桌面一键部署
.\DEPLOY_WINDOWS.ps1

# 4. 等待自动完成（30-60分钟）
```

### **步骤3: 验证部署**
```powershell
# 验证桌面部署状态
.\VERIFY_DESKTOP_DEPLOYMENT.ps1
```

### **步骤4: 启动训练**
```powershell
# 进入训练目录
cd "C:\Users\<USER>\Desktop\BeeSegmentation\training_refactored"

# 启动训练
.\start_windows_training.ps1
```

---

## 📁 **桌面目录结构**

### **部署完成后的桌面结构**
```
C:\Users\<USER>\Desktop\
├── 📁 bee_segmentation_windows_download\    # 原始下载包
│   ├── DEPLOY_WINDOWS.ps1                   # 部署脚本
│   ├── VERIFY_DESKTOP_DEPLOYMENT.ps1       # 验证脚本
│   └── ...                                 # 其他文件
└── 📁 BeeSegmentation\                      # 部署后的项目
    ├── 📁 training_refactored\              # 训练代码
    ├── 📁 experiments\                      # MAE权重
    ├── 📁 reprocessed_data\                 # 训练数据
    ├── 📁 bee_experiment\                   # Python虚拟环境
    └── 📄 *.ps1                             # Windows脚本
```

### **桌面快捷访问**
- **项目文件夹**: 双击桌面上的 `BeeSegmentation` 文件夹
- **训练目录**: `BeeSegmentation\training_refactored`
- **配置文件**: `BeeSegmentation\training_refactored\configs`
- **日志文件**: `BeeSegmentation\training_refactored\logs`

---

## 🔧 **桌面环境特殊配置**

### **权限设置**
桌面部署脚本会自动：
- ✅ 创建桌面项目目录
- ✅ 设置当前用户完全控制权限
- ✅ 确保Python虚拟环境可写
- ✅ 配置训练日志目录权限

### **路径配置**
所有配置文件已预配置桌面路径：
```yaml
# 示例配置路径
mae_checkpoint: "C:\\Users\\<USER>\\Desktop\\BeeSegmentation\\experiments\\..."
train_dir: "C:\\Users\\<USER>\\Desktop\\BeeSegmentation\\reprocessed_data\\..."
val_dir: "C:\\Users\\<USER>\\Desktop\\BeeSegmentation\\reprocessed_data\\..."
```

### **环境变量**
桌面部署会设置：
```powershell
$env:PYTHONPATH = "C:\Users\<USER>\Desktop\BeeSegmentation\training_refactored"
$env:PROJECT_ROOT = "C:\Users\<USER>\Desktop\BeeSegmentation"
```

---

## ✅ **验证清单**

### **部署前检查**
- [ ] 桌面有足够空间 (>5GB)
- [ ] 以管理员身份运行PowerShell
- [ ] 下载包完整解压
- [ ] 网络连接正常（用于下载依赖）

### **部署后验证**
```powershell
# 运行验证脚本
.\VERIFY_DESKTOP_DEPLOYMENT.ps1

# 检查项目：
# ✅ 项目目录结构完整
# ✅ MAE权重文件存在 (1.3GB)
# ✅ 训练数据完整 (2.4GB)
# ✅ Python虚拟环境正常
# ✅ CUDA和PyTorch可用
# ✅ 配置文件路径正确
# ✅ 目录权限正常
```

---

## 🎮 **RTX 5070桌面优化**

### **桌面环境优化配置**
```yaml
# 针对桌面RTX 5070的优化
training:
  batch_size: 6              # 桌面环境推荐批次
  val_batch_size: 3          # 验证批次
  mixed_precision: true      # 启用混合精度
  
system:
  device: "cuda:0"           # 使用主显卡
  max_memory_usage: 0.85     # 桌面环境显存使用率
  
desktop_optimizations:
  background_training: true   # 后台训练模式
  low_priority: false        # 正常优先级
  auto_pause: false          # 不自动暂停
```

### **桌面性能监控**
```powershell
# 启动性能监控
cd "C:\Users\<USER>\Desktop\BeeSegmentation\training_refactored"
python scripts\monitor_rtx5070.py

# 监控内容：
# - GPU使用率和温度
# - 显存使用情况
# - 训练速度和进度
# - 系统资源占用
```

---

## 🆘 **桌面环境故障排除**

### **常见桌面问题**

#### **1. 权限问题**
```powershell
# 症状：无法创建文件或目录
# 解决：重新设置权限
$path = "C:\Users\<USER>\Desktop\BeeSegmentation"
$acl = Get-Acl $path
$user = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
$rule = New-Object System.Security.AccessControl.FileSystemAccessRule($user, "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($rule)
Set-Acl -Path $path -AclObject $acl
```

#### **2. 桌面空间不足**
```powershell
# 检查桌面空间
Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"} | Select-Object Size,FreeSpace

# 清理空间：
# - 删除临时文件
# - 清理下载目录
# - 移动大文件到其他盘
```

#### **3. 虚拟环境问题**
```powershell
# 重新创建虚拟环境
cd "C:\Users\<USER>\Desktop\BeeSegmentation"
Remove-Item -Recurse -Force bee_experiment
python -m venv bee_experiment
& ".\bee_experiment\Scripts\Activate.ps1"
pip install -r requirements.txt
```

### **桌面环境最佳实践**

#### **训练期间建议**
- 🔇 关闭不必要的桌面程序
- 🔋 设置高性能电源计划
- 🛡️ 暂时关闭实时杀毒软件
- 📱 避免频繁使用桌面应用

#### **监控和维护**
- 📊 定期检查训练进度
- 🌡️ 监控GPU温度
- 💾 定期备份训练检查点
- 📝 查看训练日志

---

## 🎉 **成功标志**

当您看到以下输出时，表示桌面部署成功：

```
🎉 Windows桌面部署成功完成!
项目路径: C:\Users\<USER>\Desktop\BeeSegmentation
桌面位置: C:\Users\<USER>\Desktop\BeeSegmentation
启动训练: cd "C:\Users\<USER>\Desktop\BeeSegmentation\training_refactored" && .\start_windows_training.ps1
快捷方式: 双击桌面上的BeeSegmentation文件夹
```

**恭喜！您现在可以在桌面环境中开始8类蜂巢语义分割训练了！** 🚀

---

## 📞 **桌面技术支持**

如遇桌面环境特定问题：
1. 运行验证脚本：`.\VERIFY_DESKTOP_DEPLOYMENT.ps1`
2. 检查桌面权限和空间
3. 查看部署日志文件
4. 确认GPU驱动和CUDA环境
5. 验证Python虚拟环境状态
