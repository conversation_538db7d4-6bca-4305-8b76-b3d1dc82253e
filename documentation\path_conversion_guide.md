# Windows路径转换指南
## 8类蜂巢语义分割训练项目

---

## 🔧 **需要修改的配置文件**

### **1. 主配置文件**
```yaml
文件：training_refactored/configs/semantic_segmentation_config_correct.yaml

需要修改的路径：
Linux路径 → Windows路径

# MAE权重路径
mae_checkpoint: "/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/old_training_backup/best_checkpoint_epoch_0572.pth"
→ mae_checkpoint: "C:\\BeeSegmentation\\experiments\\phase6_mae_vit_base\\outputs\\old_training_backup\\best_checkpoint_epoch_0572.pth"

# 训练数据路径
train_dir: "/home/<USER>/PycharmProjects/20250629/reprocessed_data/patches/train"
→ train_dir: "C:\\BeeSegmentation\\reprocessed_data\\patches\\train"

# 验证数据路径
val_dir: "/home/<USER>/PycharmProjects/20250629/reprocessed_data/patches/val"
→ val_dir: "C:\\BeeSegmentation\\reprocessed_data\\patches\\val"

# 原始图像路径
image_dir: "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/static/uploads"
→ image_dir: "C:\\BeeSegmentation\\bee_cell_annotation\\static\\uploads"

# 原始掩码路径
mask_dir: "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation/data/processed_masks"
→ mask_dir: "C:\\BeeSegmentation\\bee_cell_annotation\\data\\processed_masks"
```

### **2. 其他配置文件**
```yaml
文件：training_refactored/configs/safe_upernet_training.yaml
文件：training_refactored/configs/ultra_safe_segmentation.yaml
文件：training_refactored/core/config/configs/default_config.yaml

相同的路径替换规则适用
```

---

## 🔄 **自动路径转换脚本**

### **PowerShell路径转换脚本**
```powershell
# 文件：convert_paths.ps1
param(
    [string]$ProjectPath = "C:\Users\<USER>\Desktop\BeeSegmentation\training_refactored"
)

Write-Host "🔄 开始转换配置文件路径..." -ForegroundColor Green

# 路径映射表
$pathMappings = @{
    "/home/<USER>/PycharmProjects/20250629" = "C:\Users\<USER>\Desktop\BeeSegmentation"
    "/home/<USER>/PycharmProjects/20250629/experiments" = "C:\Users\<USER>\Desktop\BeeSegmentation\experiments"
    "/home/<USER>/PycharmProjects/20250629/reprocessed_data" = "C:\Users\<USER>\Desktop\BeeSegmentation\reprocessed_data"
    "/home/<USER>/PycharmProjects/20250629/bee_cell_annotation" = "C:\Users\<USER>\Desktop\BeeSegmentation\bee_cell_annotation"
    "/home/<USER>/PycharmProjects/20250629/training_refactored" = "C:\Users\<USER>\Desktop\BeeSegmentation\training_refactored"
}

# 获取所有配置文件
$configFiles = Get-ChildItem -Path "$ProjectPath\configs" -Filter "*.yaml" -Recurse
$configFiles += Get-ChildItem -Path "$ProjectPath\core\config" -Filter "*.yaml" -Recurse -ErrorAction SilentlyContinue

foreach ($file in $configFiles) {
    Write-Host "处理文件: $($file.FullName)" -ForegroundColor Cyan
    
    $content = Get-Content $file.FullName -Raw
    $modified = $false
    
    foreach ($mapping in $pathMappings.GetEnumerator()) {
        if ($content -match [regex]::Escape($mapping.Key)) {
            $content = $content -replace [regex]::Escape($mapping.Key), $mapping.Value
            $modified = $true
        }
    }
    
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  ✅ 路径已更新" -ForegroundColor Green
    } else {
        Write-Host "  ⚪ 无需更新" -ForegroundColor Gray
    }
}

Write-Host "✅ 路径转换完成!" -ForegroundColor Green
```

---

## 📝 **手动修改清单**

### **关键文件路径替换**
```
1. training_refactored/configs/semantic_segmentation_config_correct.yaml
   - 第7行：mae_checkpoint路径
   - 第32行：train_dir路径
   - 第33行：val_dir路径
   - 第36行：image_dir路径
   - 第37行：mask_dir路径

2. training_refactored/configs/safe_upernet_training.yaml
   - 第8行：mae_checkpoint路径
   - 第32行：train_dir路径
   - 第33行：val_dir路径

3. training_refactored/configs/ultra_safe_segmentation.yaml
   - MAE权重路径
   - 数据目录路径

4. training_refactored/core/config/configs/default_config.yaml
   - 第20行：mae_checkpoint_path
   - 第25行：data_root
```

### **Python代码中的硬编码路径**
```python
# 需要检查的Python文件
training_refactored/scripts/train_semantic_segmentation.py
training_refactored/core/models/*.py
training_refactored/utils/*.py

# 查找模式
"/home/<USER>/PycharmProjects/20250629"
"~/PycharmProjects/20250629"
```

---

## 🔍 **路径验证脚本**

### **Windows路径验证**
```powershell
# 文件：verify_paths.ps1
param(
    [string]$ProjectPath = "C:\Users\<USER>\Desktop\BeeSegmentation"
)

Write-Host "🔍 验证Windows路径..." -ForegroundColor Green

$criticalPaths = @{
    "MAE权重" = "$ProjectPath\experiments\phase6_mae_vit_base\outputs\old_training_backup\best_checkpoint_epoch_0572.pth"
    "训练数据" = "$ProjectPath\reprocessed_data\patches\train"
    "验证数据" = "$ProjectPath\reprocessed_data\patches\val"
    "训练脚本" = "$ProjectPath\training_refactored\scripts\train_semantic_segmentation.py"
    "主配置" = "$ProjectPath\training_refactored\configs\semantic_segmentation_config_correct.yaml"
}

$missingPaths = @()
foreach ($item in $criticalPaths.GetEnumerator()) {
    if (Test-Path $item.Value) {
        Write-Host "✅ $($item.Key): $($item.Value)" -ForegroundColor Green
    } else {
        Write-Host "❌ $($item.Key): $($item.Value)" -ForegroundColor Red
        $missingPaths += $item.Value
    }
}

if ($missingPaths.Count -eq 0) {
    Write-Host "🎉 所有关键路径验证通过!" -ForegroundColor Green
} else {
    Write-Host "⚠️ 发现 $($missingPaths.Count) 个缺失路径" -ForegroundColor Yellow
}
```

---

## 📋 **完整路径映射表**

```
Linux路径                                                    → Windows路径
/home/<USER>/PycharmProjects/20250629                          → C:\Users\<USER>\Desktop\BeeSegmentation
/home/<USER>/PycharmProjects/20250629/training_refactored      → C:\Users\<USER>\Desktop\BeeSegmentation\training_refactored
/home/<USER>/PycharmProjects/20250629/experiments              → C:\Users\<USER>\Desktop\BeeSegmentation\experiments
/home/<USER>/PycharmProjects/20250629/reprocessed_data         → C:\Users\<USER>\Desktop\BeeSegmentation\reprocessed_data
/home/<USER>/PycharmProjects/20250629/bee_cell_annotation      → C:\Users\<USER>\Desktop\BeeSegmentation\bee_cell_annotation

具体文件：
MAE权重：
/home/<USER>/PycharmProjects/20250629/experiments/phase6_mae_vit_base/outputs/old_training_backup/best_checkpoint_epoch_0572.pth
→ C:\Users\<USER>\Desktop\BeeSegmentation\experiments\phase6_mae_vit_base\outputs\old_training_backup\best_checkpoint_epoch_0572.pth

训练数据：
/home/<USER>/PycharmProjects/20250629/reprocessed_data/patches/train
→ C:\Users\<USER>\Desktop\BeeSegmentation\reprocessed_data\patches\train

验证数据：
/home/<USER>/PycharmProjects/20250629/reprocessed_data/patches/val
→ C:\Users\<USER>\Desktop\BeeSegmentation\reprocessed_data\patches\val
```

---

## ⚠️ **注意事项**

1. **路径分隔符**：Linux使用`/`，Windows使用`\`
2. **转义字符**：YAML文件中使用`\\`或单引号避免转义
3. **大小写敏感**：Windows路径不区分大小写，但建议保持一致
4. **长路径支持**：确保Windows支持长路径（>260字符）
5. **权限问题**：确保对目标目录有读写权限

---

## 🚀 **快速转换命令**

### **一键路径转换**
```powershell
# 下载项目后立即执行
cd C:\Users\<USER>\Desktop\BeeSegmentation\training_refactored
.\convert_paths.ps1
.\verify_paths.ps1
```

这样就完成了从Linux到Windows的完整路径转换！
