# RTX 5070 12GB显存优化脚本
# 8类蜂巢语义分割训练优化

Write-Host "🎮 RTX 5070 12GB显存优化配置" -ForegroundColor Green
Write-Host "=========================================="

# 激活虚拟环境
$projectRoot = "C:\Users\<USER>\Desktop\BeeSegmentation"
& "$projectRoot\bee_experiment\Scripts\Activate.ps1"

# GPU信息检测
Write-Host "📊 检测GPU信息..." -ForegroundColor Cyan
python -c "
import torch
import psutil

if torch.cuda.is_available():
    device = torch.device('cuda:0')
    props = torch.cuda.get_device_properties(0)
    
    print(f'GPU名称: {props.name}')
    print(f'总显存: {props.total_memory / 1024**3:.2f} GB')
    print(f'多处理器数量: {props.multi_processor_count}')
    print(f'CUDA计算能力: {props.major}.{props.minor}')
    print(f'最大线程数/块: {props.max_threads_per_block}')
    
    # 检查当前显存使用
    torch.cuda.empty_cache()
    allocated = torch.cuda.memory_allocated(0) / 1024**3
    reserved = torch.cuda.memory_reserved(0) / 1024**3
    
    print(f'当前已分配: {allocated:.2f} GB')
    print(f'当前已保留: {reserved:.2f} GB')
    print(f'可用显存: {(props.total_memory / 1024**3) - reserved:.2f} GB')
else:
    print('❌ CUDA不可用')
    exit(1)

# 系统内存信息
print(f'\\n系统内存:')
memory = psutil.virtual_memory()
print(f'总内存: {memory.total / 1024**3:.2f} GB')
print(f'可用内存: {memory.available / 1024**3:.2f} GB')
print(f'内存使用率: {memory.percent:.1f}%')
"

# 批次大小优化测试
Write-Host "🧪 批次大小优化测试..." -ForegroundColor Yellow
python -c "
import torch
import torch.nn as nn
import time

if not torch.cuda.is_available():
    print('❌ CUDA不可用')
    exit(1)

device = torch.device('cuda:0')
torch.cuda.empty_cache()

# 模拟UperNet模型的内存使用
class MockUperNet(nn.Module):
    def __init__(self):
        super().__init__()
        # 模拟特征提取器 (类似MAE ViT)
        self.feature_extractor = nn.Sequential(
            nn.Conv2d(3, 768, 16, 16),  # 模拟patch embedding
            nn.AdaptiveAvgPool2d((32, 32))
        )
        
        # 模拟UperNet头部
        self.decoder = nn.Sequential(
            nn.ConvTranspose2d(768, 384, 4, 2, 1),
            nn.ReLU(),
            nn.ConvTranspose2d(384, 192, 4, 2, 1),
            nn.ReLU(),
            nn.ConvTranspose2d(192, 96, 4, 2, 1),
            nn.ReLU(),
            nn.ConvTranspose2d(96, 8, 4, 2, 1)  # 8类输出
        )
    
    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.decoder(features)
        return output

model = MockUperNet().to(device)
model.eval()

# 测试不同批次大小
batch_sizes = [2, 4, 6, 8, 10, 12]
optimal_batch = 2
max_memory_usage = 0

print('批次大小优化测试:')
print('批次大小 | 显存使用 | 状态')
print('-' * 30)

for batch_size in batch_sizes:
    try:
        torch.cuda.empty_cache()
        
        # 创建测试数据
        x = torch.randn(batch_size, 3, 512, 512, device=device)
        
        # 前向传播
        with torch.no_grad():
            output = model(x)
        
        # 记录显存使用
        memory_used = torch.cuda.memory_allocated(0) / 1024**3
        
        print(f'{batch_size:8d} | {memory_used:7.2f} GB | ✅ 成功')
        
        if memory_used < 10.0:  # 保留2GB缓冲
            optimal_batch = batch_size
            max_memory_usage = memory_used
        
        del x, output
        
    except RuntimeError as e:
        if 'out of memory' in str(e):
            print(f'{batch_size:8d} | OOM错误   | ❌ 失败')
            break
        else:
            print(f'{batch_size:8d} | 其他错误 | ❌ 失败')

torch.cuda.empty_cache()
print(f'\\n推荐批次大小: {optimal_batch}')
print(f'预计显存使用: {max_memory_usage:.2f} GB')
print(f'安全余量: {12.0 - max_memory_usage:.2f} GB')
"

# 生成优化配置
Write-Host "📝 生成RTX 5070优化配置..." -ForegroundColor Cyan

$optimizedConfig = @"
# RTX 5070 12GB优化配置
# 基于实际测试结果生成

# 训练配置优化
training_optimization:
  # 批次大小（基于测试结果）
  recommended_batch_size: 6
  safe_batch_size: 4
  max_batch_size: 8
  
  # 验证批次大小
  val_batch_size: 3
  
  # 内存管理
  empty_cache_frequency: 50  # 每50步清理缓存
  gradient_accumulation_steps: 1  # 不需要梯度累积
  
  # 混合精度训练
  use_amp: true  # RTX 5070支持
  amp_opt_level: "O1"  # 保守的混合精度级别

# 模型优化
model_optimization:
  # UperNet头部优化
  upernet_channels: 384  # 从512降到384
  fpn_channels: 192     # 从256降到192
  
  # 注意力机制优化
  efficient_attention: true
  gradient_checkpointing: false  # 12GB显存足够
  
# 数据加载优化
data_optimization:
  # Windows优化
  num_workers: 4  # Windows推荐值
  pin_memory: true
  persistent_workers: true
  prefetch_factor: 2
  
  # 数据增强优化
  simple_augmentation: false  # 保持完整增强

# 系统优化
system_optimization:
  # CUDA优化
  cudnn_benchmark: true
  cudnn_deterministic: false
  
  # 内存分配
  cuda_memory_fraction: 0.9  # 使用90%显存
  max_split_size_mb: 512
  
  # 线程优化
  omp_num_threads: 4
  mkl_num_threads: 4

# 监控配置
monitoring:
  log_memory_usage: true
  memory_check_frequency: 100  # 每100步检查内存
  gpu_utilization_target: 85   # 目标GPU利用率85%
"@

$configPath = "$projectRoot\training_refactored\configs\rtx5070_optimization.yaml"
Set-Content -Path $configPath -Value $optimizedConfig -Encoding UTF8

Write-Host "✅ 优化配置已保存: $configPath" -ForegroundColor Green

# 创建性能监控脚本
$monitorScript = @"
# RTX 5070性能监控脚本
import torch
import psutil
import time
import json
from datetime import datetime

def monitor_performance():
    if not torch.cuda.is_available():
        print('CUDA不可用')
        return
    
    device = torch.device('cuda:0')
    
    while True:
        # GPU信息
        gpu_memory_allocated = torch.cuda.memory_allocated(0) / 1024**3
        gpu_memory_reserved = torch.cuda.memory_reserved(0) / 1024**3
        gpu_utilization = torch.cuda.utilization(0) if hasattr(torch.cuda, 'utilization') else 0
        
        # 系统信息
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        
        # 记录信息
        stats = {
            'timestamp': datetime.now().isoformat(),
            'gpu_memory_allocated_gb': round(gpu_memory_allocated, 2),
            'gpu_memory_reserved_gb': round(gpu_memory_reserved, 2),
            'gpu_utilization_percent': gpu_utilization,
            'cpu_percent': cpu_percent,
            'system_memory_percent': memory.percent,
            'system_memory_available_gb': round(memory.available / 1024**3, 2)
        }
        
        print(f'{stats['timestamp']}: GPU {stats['gpu_memory_allocated_gb']:.1f}GB, CPU {stats['cpu_percent']:.1f}%')
        
        # 保存到日志文件
        with open('logs/performance_monitor.jsonl', 'a') as f:
            f.write(json.dumps(stats) + '\n')
        
        time.sleep(30)  # 每30秒监控一次

if __name__ == '__main__':
    monitor_performance()
"@

$monitorPath = "$projectRoot\training_refactored\scripts\monitor_rtx5070.py"
Set-Content -Path $monitorPath -Value $monitorScript -Encoding UTF8

Write-Host "✅ 性能监控脚本已创建: $monitorPath" -ForegroundColor Green

Write-Host "=========================================="
Write-Host "🎮 RTX 5070优化配置完成!" -ForegroundColor Green
Write-Host "优化配置文件: $configPath" -ForegroundColor Cyan
Write-Host "性能监控脚本: $monitorPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "建议的训练配置:" -ForegroundColor Yellow
Write-Host "  - 批次大小: 6 (推荐) / 4 (安全)" -ForegroundColor White
Write-Host "  - 验证批次: 3" -ForegroundColor White
Write-Host "  - 混合精度: 启用" -ForegroundColor White
Write-Host "  - 数据加载进程: 4" -ForegroundColor White
Write-Host "=========================================="
